import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router';
import { But<PERSON> } from '@repo/ui/components/button';
// import { Card, CardContent } from '@repo/ui/components/card';
// import { Badge } from '@repo/ui/components/badge';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Alert, AlertDescription } from '@repo/ui/components/alert';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@repo/ui/components/dialog';
import { Input } from '@repo/ui/components/input';

import { Checkbox } from '@repo/ui/components/checkbox';
import { Badge } from '@repo/ui/components/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@repo/ui/components/table';
import { Tooltip, TooltipContent, TooltipTrigger } from '@repo/ui/components/tooltip';
import { ArrowLeft, Menu, Search, ChevronLeft, ChevronRight, ArrowRight, X, RotateCcw, Plus, ChevronDown } from 'lucide-react';
import { USER_ROUTES } from '@/app.routes';
import axiosInstance from '@/lib/axios';
import { toast } from 'sonner';

interface User {
  id: number;
  first_name: string;
  last_name: string;
  emp_id: string;
  email: string;
  department: string;
  location: string;
  survey: string | number;
  response_id?: string;
  survey_response?: string | null; // Can be null as seen in the console
  metadata?: any; // Metadata object containing all custom fields
  rater?: {
    first_name: string;
    last_name: string;
    emp_id: string;
  };
  target?: {
    first_name: string;
    last_name: string;
    emp_id: string;
    metadata?: any; // Metadata object for target user
  };
}

interface SurveyInfo {
  id: string;
  title: string;
  isPairsEditable: boolean;
  isDeclinable: boolean;
}

interface StatusCounts {
  TODO: number;
  PROG: number;
  SUBM: number;
  DECL: number;
}

// FilterTag Component for individual filter management
const FilterTag: React.FC<{
  field: string;
  displayName: string;
  values: string[];
  availableValues: string[];
  onValueChange: (value: string, checked: boolean) => void;
  onRemove: () => void;
}> = ({ field, displayName, values, availableValues, onValueChange, onRemove }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const displayText = values.length > 0
    ? `${displayName} (${values.length})`
    : displayName;

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <Badge
        variant={values.length > 0 ? "default" : "secondary"}
        className={`cursor-pointer hover:bg-blue-600 hover:text-white dark:hover:bg-blue-500 px-3 py-1 text-sm transition-colors ${
          values.length > 0 ? 'bg-blue-600 text-white' : ''
        }`}
        onClick={handleToggle}
      >
        {displayText}
        <ChevronDown className="h-3 w-3 ml-1" />
      </Badge>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-64 bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-md shadow-lg z-50 p-3">
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-sm">{displayName}</h4>
                {values.length > 0 && (
                  <p className="text-xs text-blue-600 dark:text-blue-400">
                    {values.length} selected: {values.slice(0, 2).join(', ')}
                    {values.length > 2 && ` +${values.length - 2} more`}
                  </p>
                )}
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onRemove}
                className="h-6 w-6 p-0 hover:bg-destructive hover:text-destructive-foreground"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>

            <div className="flex gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  availableValues.forEach(value => {
                    if (!values.includes(value)) {
                      onValueChange(value, true);
                    }
                  });
                }}
                className="h-6 px-2 text-xs"
              >
                Select All
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {
                  values.forEach(value => {
                    onValueChange(value, false);
                  });
                }}
                className="h-6 px-2 text-xs"
              >
                Clear All
              </Button>
            </div>

            <div className="space-y-2 max-h-48 overflow-y-auto">
              {availableValues.map((value) => {
                const isSelected = values.includes(value);
                return (
                  <div
                    key={value}
                    className={`flex items-center space-x-2 p-2 rounded hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
                      isSelected ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700' : ''
                    }`}
                  >
                    <Checkbox
                      id={`${field}-${value}`}
                      checked={isSelected}
                      onCheckedChange={(checked) =>
                        onValueChange(value, checked as boolean)
                      }
                    />
                    <label
                      htmlFor={`${field}-${value}`}
                      className={`text-sm cursor-pointer flex-1 ${
                        isSelected ? 'font-medium text-blue-700 dark:text-blue-300' : ''
                      }`}
                    >
                      {value}
                    </label>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

// AddFilter Component
const AddFilterDropdown: React.FC<{
  availableFields: any[];
  onAddFilter: (field: string) => void;
}> = ({ availableFields, onAddFilter }) => {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = React.useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsOpen(!isOpen);
  };

  const handleSelectField = (field: string) => {
    onAddFilter(field);
    setIsOpen(false);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <Button
        variant="outline"
        size="sm"
        className="h-8 border-dashed"
        onClick={handleToggle}
      >
        <Plus className="h-4 w-4 mr-1" />
        Add filter
      </Button>

      {isOpen && (
        <div className="absolute top-full left-0 mt-1 w-48 bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-md shadow-lg z-50 p-2">
          <div className="space-y-1">
            {availableFields.map((label) => (
              <Button
                key={label.field}
                variant="ghost"
                size="sm"
                className="w-full justify-start h-8 px-2"
                onClick={() => handleSelectField(label.field)}
              >
                {label.display_name}
              </Button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const Pairings: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(true);
  const [users, setUsers] = useState<User[]>([]);
  const [surveyInfo, setSurveyInfo] = useState<SurveyInfo | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [status, setStatus] = useState('TODO');
  const [statusCounts, setStatusCounts] = useState<StatusCounts>({
    TODO: 0,
    PROG: 0,
    SUBM: 0,
    DECL: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [selectAll, setSelectAll] = useState(false);
  const [showDeclineModal, setShowDeclineModal] = useState(false);
  const [userToDecline, setUserToDecline] = useState<User | null>(null);
  const [showSidebar, setShowSidebar] = useState(false);
  const [ordering] = useState('first_name');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [pageSize] = useState(15);
  const [metadataLabels, setMetadataLabels] = useState<any[]>([]);
  // Multi-select filter state
  const [activeFilters, setActiveFilters] = useState<Record<string, string[]>>({});

  useEffect(() => {
    if (id) {
      fetchSurveyInfo();
      fetchStatusCounts();
      fetchMetadataLabels();
    }
  }, [id]);

  useEffect(() => {
    if (id) {
      fetchUsers();
    }
  }, [id, status, searchTerm, ordering, currentPage, activeFilters]);



  // Helper functions for multi-select filters
  const handleFilterValueChange = (field: string, value: string, checked: boolean) => {
    setActiveFilters(prev => {
      const currentValues = prev[field] || [];
      let newValues;
      if (checked) {
        newValues = [...currentValues, value];
      } else {
        newValues = currentValues.filter(v => v !== value);
      }
      return {
        ...prev,
        [field]: newValues
      };
    });
    setCurrentPage(1); // Reset to first page when filtering
  };

  const handleRemoveFilter = (field: string) => {
    setActiveFilters(prev => {
      const newFilters = { ...prev };
      delete newFilters[field];
      return newFilters;
    });
    setCurrentPage(1);
  };

  const handleAddFilter = (field: string) => {
    setActiveFilters(prev => ({
      ...prev,
      [field]: []
    }));
  };

  const handleClearAllFilters = () => {
    setActiveFilters({});
    setCurrentPage(1);
  };

  // Get available fields that haven't been added yet
  const availableFields = metadataLabels.filter(
    label => !Object.keys(activeFilters).includes(label.field)
  );

  const fetchSurveyInfo = async () => {
    try {
      const response = await axiosInstance.get(`/survey/index/${id}/`);
      setSurveyInfo(response.data);
    } catch (err) {
      console.error('Error fetching survey info:', err);
      setError('Failed to load survey information');
    }
  };

  const fetchStatusCounts = async () => {
    try {
      // Fetch counts for each status
      const statuses = ['TODO', 'PROG', 'SUBM', 'DECL'];
      const counts: StatusCounts = { TODO: 0, PROG: 0, SUBM: 0, DECL: 0 };

      for (const statusKey of statuses) {
        const response = await axiosInstance.get('/survey/pairing/', {
          params: {
            index_id: id,
            status: statusKey,
            page_size: 1,
            exclude_disabled: true
          }
        });
        counts[statusKey as keyof StatusCounts] = response.data.count_items || 0;
      }

      setStatusCounts(counts);
    } catch (err) {
      console.error('Error fetching status counts:', err);
    }
  };

  const fetchMetadataLabels = async () => {
    try {
      const response = await axiosInstance.get('/staff/customers/metadata-labels/', {
        params: {
          survey_id: id,
          exclude_disabled: true
        }
      });

      // Transform the response to include options from values object (similar to legacy app)
      const labels = response.data.labels || [];
      const values = response.data.values || {};

      const transformedLabels = labels.map((label: any) => ({
        ...label,
        options: values[label.field] || []
      }));

      setMetadataLabels(transformedLabels);
    } catch (err) {
      console.error('Error fetching metadata labels:', err);
      setMetadataLabels([]);
    }
  };

  const fetchUsers = async () => {
    try {
      setIsLoading(true);

      // Build metadata filter params from multi-select filters
      const metadataParams: any = {};
      Object.entries(activeFilters).forEach(([field, values]) => {
        if (values.length > 0) {
          // Join multiple values with comma for API
          metadataParams[field] = values.join(',');
        }
      });

      // Use different endpoints based on status (similar to legacy app)
      const response = status !== "ADD"
        ? await axiosInstance.get('/survey/pairing/', {
            params: {
              index_id: id,
              status,
              page_size: pageSize,
              page: currentPage,
              ordering,
              exclude_disabled: true,
              ...(searchTerm && { search: searchTerm }),
              ...metadataParams
            }
          })
        : await axiosInstance.get('/survey/pairing/add/', {
            params: {
              index_id: id,
              page_size: pageSize,
              page: currentPage,
              ordering,
              ...(searchTerm && { search: searchTerm }),
              ...metadataParams
            }
          });

      // Debug: Log the response to see the actual structure
      console.log('Pairing API response:', response.data);
      if (response.data.results && response.data.results.length > 0) {
        console.log('First user data:', response.data.results[0]);
      }

      setUsers(response.data.results || []);
      setTotalPages(response.data.count_pages || 1);
      setTotalItems(response.data.count_items || 0);
      setError(null);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load pairings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleStartSurvey = async (user: User) => {
    try {
      // Check if response_id already exists
      const responseId = user.response_id || user.survey_response;

      if (responseId && user.survey) {
        // Navigate directly using the existing response_id and survey_id
        navigate(USER_ROUTES().dashboard.upwardReview.getTakeSurveyUrl(responseId, user.survey.toString()));
      } else if (user.survey) {
        // If no response_id exists, create a new survey response (similar to legacy app)
        console.log('Creating new survey response for user:', user);

        const createResponsePayload = {
          survey: user.survey,
          pairing: user.id,
          // status: "PROG", // Uncomment if needed
        };

        const response = await axiosInstance.post('/survey/survey-response/', createResponsePayload);

        // Navigate using the newly created response_id
        navigate(USER_ROUTES().dashboard.upwardReview.getTakeSurveyUrl(
          response.data.id,
          response.data.survey || user.survey.toString()
        ));
      } else {
        console.error('No survey ID found for user:', user);
        setError('Survey information not found. Please contact support.');
      }
    } catch (err) {
      console.error('Error starting survey:', err);
      setError('Failed to start survey');
    }
  };

  const handleAddUsersToSurvey = async () => {
    try {
      if (selectedUsers.length === 0) {
        setError('Please select at least one user to add');
        return;
      }

      // Add multiple users to survey pairing (similar to legacy app)
      const promises = selectedUsers.map(userId =>
        axiosInstance.post('/survey/pairing/', {
          index: id, // Use 'index' instead of 'index_id' based on the error message
          target: userId
        })
      );

      await Promise.all(promises);

      // Clear selections
      setSelectedUsers([]);
      setSelectAll(false);

      // Refresh the users list to show updated data
      fetchUsers();

      // Switch back to TODO status to show the newly added pairings
      setStatus('TODO');
    } catch (err) {
      console.error('Error adding users to survey:', err);
      setError('Failed to add users to survey');
    }
  };

  const handleUserSelect = (userId: number) => {
    setSelectedUsers(prev =>
      prev.includes(userId)
        ? prev.filter(id => id !== userId)
        : [...prev, userId]
    );
  };

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedUsers(users.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  const handleDeclineUser = (user: User) => {
    setUserToDecline(user);
    setShowDeclineModal(true);
  };

  const handleBulkDecline = () => {
    if (selectedUsers.length === 0) {
      setError('Please select at least one user to decline');
      return;
    }
    setUserToDecline(null); // null indicates bulk decline
    setShowDeclineModal(true);
  };

  const confirmDecline = async () => {
    try {
      if (userToDecline) {
        // Single user decline
        await declineUser(userToDecline);
      } else {
        // Bulk decline
        const usersToDecline = users.filter(user => selectedUsers.includes(user.id));
        for (const user of usersToDecline) {
          await declineUser(user);
        }
        // Clear selections after bulk decline
        setSelectedUsers([]);
        setSelectAll(false);
      }

      // Refresh data and counts
      fetchUsers();
      fetchStatusCounts();
      setShowDeclineModal(false);
      setUserToDecline(null);

      // Success message is now handled in individual declineUser function
      if (!userToDecline) {
        // For bulk decline, show additional success message
        toast.success(`${selectedUsers.length} pairing(s) declined successfully`);
      }
    } catch (err) {
      console.error('Error declining pairing:', err);
      // Error message is now handled in individual declineUser function
    }
  };

  const declineUser = async (user: User) => {
    try {
      console.log('Declining user:', user);

      // Create response if it doesn't exist
      let responseId = user.response_id || user.survey_response;
      if (!responseId) {
        console.log('Creating new survey response for user:', user.id);
        const createResponsePayload = {
          survey: typeof user.survey === 'string' ? parseInt(user.survey) : user.survey,
          pairing: user.id,
          resourcetype: "SurveyResponseUpward",
        };
        const response = await axiosInstance.post('/survey/survey-response/', createResponsePayload);
        responseId = response.data.id;
        console.log('Created response with ID:', responseId);
      }

      // Decline the pairing
      const declinePayload = {
        survey: typeof user.survey === 'string' ? parseInt(user.survey) : user.survey,
        pairing: user.id,
        resourcetype: "SurveyResponseUpward",
      };

      console.log('Declining with payload:', declinePayload);
      console.log('Using response ID:', responseId);

      const declineResponse = await axiosInstance.post(`/survey/survey-response/${responseId}/decline/`, declinePayload);
      console.log('Decline response:', declineResponse.data);

      // Show success toast
      toast.success('Pairing declined successfully');

    } catch (error) {
      console.error('Error in declineUser:', error);
      toast.error('Failed to decline pairing. Please try again.');
      throw error;
    }
  };

  const handleReinstateUser = async (user: User) => {
    console.log('=== REINSTATE FUNCTION CALLED ===');
    console.log('User object:', user);

    try {
      console.log('Reinstating user:', user);

      // Use existing survey response (declined users should always have one)
      const responseId = user.survey_response || user.response_id;
      console.log('User survey_response:', user.survey_response);
      console.log('User response_id:', user.response_id);
      console.log('Using responseId:', responseId);

      if (!responseId) {
        console.error('No survey response ID found for declined user:', user);
        toast.error('Cannot reinstate: No survey response found');
        return;
      }

      // Reinstate the pairing
      const reinstatePayload = {
        survey: typeof user.survey === 'string' ? parseInt(user.survey) : user.survey,
        pairing: user.id,
      };

      console.log('Reinstating with payload:', reinstatePayload);
      console.log('Using response ID:', responseId);

      await axiosInstance.post(`/survey/survey-response/${responseId}/reinstate/`, reinstatePayload);

      // Refresh data and switch to TODO status
      fetchUsers();
      fetchStatusCounts();
      setStatus('TODO');

      // Show success toast
      toast.success('Pairing reinstated successfully');

    } catch (error) {
      console.error('Error reinstating pairing:', error);
      toast.error('Failed to reinstate pairing. Please try again.');
    }
  };

  const getStatusLabel = (statusKey: string) => {
    const statusMap = {
      TODO: 'To-Do',
      PROG: 'In-Progress',
      SUBM: 'Completed',
      DECL: 'Declined'
    };
    return statusMap[statusKey as keyof typeof statusMap] || statusKey;
  };

  if (error) {
    return (
      <div className="p-6 bg-white dark:bg-gray-900">
        <Alert variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  // Sidebar Component
  const Sidebar = () => (
    <div className={`
      fixed inset-y-0 left-0 z-40 w-80 bg-white dark:bg-gray-800 border-r dark:border-gray-700 transform transition-transform duration-300 ease-in-out
      ${showSidebar ? 'translate-x-0' : '-translate-x-full'}
      lg:translate-x-0 lg:static lg:inset-0
    `}>
      <div className="p-6 space-y-6">
        {/* Back Button */}
        <Button
          variant="ghost"
          onClick={() => navigate(USER_ROUTES().dashboard.surveyList)}
          className="w-full justify-start"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>

        {/* Survey Status */}
        <div>
          <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-4">Survey Status</h3>
          <div className="space-y-2">
            {[
              { key: 'TODO', label: 'To-Do', count: statusCounts.TODO },
              { key: 'PROG', label: 'In-Progress', count: statusCounts.PROG },
              { key: 'SUBM', label: 'Completed', count: statusCounts.SUBM },
              { key: 'DECL', label: 'Declined', count: statusCounts.DECL }
            ].map((item) => (
              <div
                key={item.key}
                className={`
                  flex justify-between items-center p-3 rounded cursor-pointer transition-colors
                  ${status === item.key
                    ? 'bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 border-l-4 border-green-500 font-semibold'
                    : 'hover:bg-gray-50 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-300'
                  }
                `}
                onClick={() => {
                  setStatus(item.key);
                  setCurrentPage(1); // Reset to first page when changing status
                  setShowSidebar(false);
                }}
              >
                <span className="text-sm">{item.label}</span>
                <span className="font-semibold">
                  {isLoading ? <Skeleton className="h-4 w-6" /> : item.count}
                </span>
              </div>
            ))}
          </div>
        </div>

        {/* Add Reviewee Button - Always show for upward surveys (like legacy app) */}
        <>
          <hr className="my-4 border-gray-200 dark:border-gray-600" />
          <div className="px-3">
            <Button
              className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm"
              disabled={status === 'ADD'}
              onClick={() => {
                setStatus('ADD');
                setCurrentPage(1); // Reset to first page when switching to ADD mode
                setShowSidebar(false);
              }}
            >
              Add Reviewee
            </Button>
            <div className="mt-3 p-3 rounded text-sm" style={{ backgroundColor: '#FCEED0' }}>
              <p className="text-gray-700 m-0">
                Please click <strong>Add Reviewee</strong> to add a new person to rate.
              </p>
            </div>
          </div>
        </>

        {/* Debug info - shows survey info to help troubleshoot */}
        {/* {process.env.NODE_ENV === 'development' && (
          <div className="px-3 mt-2 text-xs text-gray-500 dark:text-gray-400">
            <details>
              <summary>Debug: Survey Info</summary>
              <pre className="mt-1 text-xs">
                {JSON.stringify(surveyInfo, null, 2)}
              </pre>
            </details>
          </div>
        )} */}
      </div>
    </div>
  );

  return (
    <div className="flex w-full h-full bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar />

      {/* Overlay for mobile */}
      {showSidebar && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden"
          onClick={() => setShowSidebar(false)}
        />
      )}

      {/* Main Content */}
      <div className="flex-1 w-full flex flex-col lg:ml-0">
        {/* Header */}
        <div className="bg-orange-500 dark:bg-orange-600 text-white px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden mr-2 text-white hover:bg-orange-600 dark:hover:bg-orange-700"
                onClick={() => setShowSidebar(true)}
              >
                <Menu className="h-4 w-4" />
              </Button>
              <div>
                <h1 className="text-xl font-semibold">
                  {surveyInfo?.title || 'Survey'} - {getStatusLabel(status)}
                </h1>
              </div>
            </div>
          </div>
        </div>

        {/* Multi-Select Filters */}
        <div className="bg-white dark:bg-gray-800 border-b dark:border-gray-700 px-6 py-4">
          <div className="flex items-center gap-3 flex-wrap">
            {/* Active Filter Tags */}
            {Object.entries(activeFilters).map(([field, values]) => {
              const fieldLabel = metadataLabels.find(label => label.field === field);
              return (
                <FilterTag
                  key={field}
                  field={field}
                  displayName={fieldLabel?.display_name || field}
                  values={values}
                  availableValues={fieldLabel?.options || []}
                  onValueChange={(value, checked) => handleFilterValueChange(field, value, checked)}
                  onRemove={() => handleRemoveFilter(field)}
                />
              );
            })}

            {/* Add Filter Dropdown */}
            {availableFields.length > 0 && (
              <AddFilterDropdown
                availableFields={availableFields}
                onAddFilter={handleAddFilter}
              />
            )}

            {/* Clear All Button */}
            {Object.keys(activeFilters).length > 0 && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearAllFilters}
                className="h-8 px-2 text-muted-foreground hover:text-foreground"
              >
                <X className="h-4 w-4 mr-1" />
                Clear all
              </Button>
            )}

            {/* Selected Count and Action Buttons */}
            {selectedUsers.length > 0 && (
              <>
                <span className="text-blue-600 dark:text-blue-400 font-medium">
                  {selectedUsers.length} User{selectedUsers.length > 1 ? 's' : ''} Selected
                </span>
                {status === 'ADD' ? (
                  <Button
                    onClick={handleAddUsersToSurvey}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                  >
                    Add
                  </Button>
                ) : (status === 'TODO' || status === 'PROG') && (
                  <Button
                    onClick={handleBulkDecline}
                    variant="destructive"
                    className="bg-red-600 hover:bg-red-700 text-white"
                  >
                    Decline
                  </Button>
                )}
              </>
            )}

            {/* Search Input */}
            <div className="flex-1 max-w-md ml-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 h-4 w-4" />
                <Input
                  placeholder="Search for name, email or emp"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setCurrentPage(1); // Reset to first page when searching
                  }}
                  className="pl-10"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Content Area */}
        <div className="flex-1 p-6">
          {isLoading ? (
            <div className="space-y-4">
              {Array(5).fill(0).map((_, index) => (
                <Skeleton key={index} className="h-12 w-full" />
              ))}
            </div>
          ) : users.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">
                There are no items to show. <button
                  className="text-blue-600 dark:text-blue-400 hover:underline"
                  onClick={() => {
                    setStatus('ADD');
                    setCurrentPage(1); // Reset to first page when switching to ADD mode
                  }}
                >
                  Click here
                </button> add a new reviewee.
              </p>
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg border dark:border-gray-700">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-12">
                      <Checkbox
                        checked={selectAll}
                        onCheckedChange={handleSelectAll}
                      />
                    </TableHead>
                    <TableHead>No.</TableHead>
                    <TableHead>Name</TableHead>
                    <TableHead>Class Year</TableHead>
                    <TableHead>Department</TableHead>
                    <TableHead>Title</TableHead>
                    <TableHead>Practice Group</TableHead>
                    <TableHead>Location</TableHead>
                    {status !== 'ADD' && <TableHead>Actions</TableHead>}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {users.map((user, index) => (
                    <TableRow key={user.id}>
                      <TableCell>
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          onCheckedChange={() => handleUserSelect(user.id)}
                        />
                      </TableCell>
                      <TableCell>{(currentPage - 1) * pageSize + index + 1}.</TableCell>
                      <TableCell
                        className={status !== 'ADD' ? "cursor-pointer text-blue-600 dark:text-blue-400 hover:underline" : ""}
                        onClick={() => status !== 'ADD' && handleStartSurvey(user)}
                      >
                        {status === 'ADD'
                          ? `${user.first_name} ${user.last_name}`
                          : `${user.target?.first_name} ${user.target?.last_name}`
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.metadata?.class_year || '-')
                          : (user.target?.metadata?.class_year || '-')
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.department || user.metadata?.department || '-')
                          : (user.target?.metadata?.department || user.department || '-')
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.metadata?.current_level || '-')
                          : (user.target?.metadata?.current_level || '-')
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.metadata?.practice_group || '-')
                          : (user.target?.metadata?.practice_group || '-')
                        }
                      </TableCell>
                      <TableCell>
                        {status === 'ADD'
                          ? (user.metadata?.office_location || '-')
                          : (user.target?.metadata?.office_location || '-')
                        }
                      </TableCell>
                      {status !== 'ADD' && (
                        <TableCell>
                          <div className="flex items-center gap-2">
                            {(status === 'TODO' || status === 'PROG') && (
                              <>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleStartSurvey(user)}
                                      className="p-1 h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                    >
                                      <ArrowRight className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Rate this person</p>
                                  </TooltipContent>
                                </Tooltip>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleDeclineUser(user)}
                                      className="p-1 h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Decline to rate</p>
                                  </TooltipContent>
                                </Tooltip>
                              </>
                            )}
                            {status === 'SUBM' && (
                              <>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleStartSurvey(user)}
                                      className="p-1 h-8 w-8 text-blue-600 hover:text-blue-700 hover:bg-blue-50"
                                    >
                                      <ArrowRight className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Rate this person</p>
                                  </TooltipContent>
                                </Tooltip>
                                <Tooltip>
                                  <TooltipTrigger asChild>
                                    <Button
                                      size="sm"
                                      variant="ghost"
                                      onClick={() => handleDeclineUser(user)}
                                      className="p-1 h-8 w-8 text-red-600 hover:text-red-700 hover:bg-red-50"
                                    >
                                      <X className="h-4 w-4" />
                                    </Button>
                                  </TooltipTrigger>
                                  <TooltipContent>
                                    <p>Decline to rate</p>
                                  </TooltipContent>
                                </Tooltip>
                              </>
                            )}
                            {status === 'DECL' && (
                              <Tooltip>
                                <TooltipTrigger asChild>
                                  <Button
                                    size="sm"
                                    variant="ghost"
                                    onClick={() => handleReinstateUser(user)}
                                    className="p-1 h-8 w-8 text-green-600 hover:text-green-700 hover:bg-green-50"
                                  >
                                    <RotateCcw className="h-4 w-4" />
                                  </Button>
                                </TooltipTrigger>
                                <TooltipContent>
                                  <p>Reinstate</p>
                                </TooltipContent>
                              </Tooltip>
                            )}
                          </div>
                        </TableCell>
                      )}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between px-6 py-4 border-t dark:border-gray-700">
                  <div className="text-sm text-gray-700 dark:text-gray-300">
                    Showing {(currentPage - 1) * pageSize + 1} to {Math.min(currentPage * pageSize, totalItems)} of {totalItems} results
                  </div>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                      disabled={currentPage === 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <span className="text-sm text-gray-700 dark:text-gray-300">
                      Page {currentPage} of {totalPages}
                    </span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                      disabled={currentPage === totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Decline Confirmation Modal */}
      <Dialog open={showDeclineModal} onOpenChange={setShowDeclineModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Decline</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            {userToDecline ? (
              <p>
                Are you sure you want to decline the pairing with{' '}
                <strong>{userToDecline.target?.first_name} {userToDecline.target?.last_name}</strong>?
              </p>
            ) : (
              <p>
                Are you sure you want to decline <strong>{selectedUsers.length}</strong> selected pairing{selectedUsers.length > 1 ? 's' : ''}?
              </p>
            )}
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
              This action cannot be undone.
            </p>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => {
                setShowDeclineModal(false);
                setUserToDecline(null);
              }}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={confirmDecline}
            >
              Decline
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Pairings;
